/**
 * Intelligence Collection System Types
 * Defines data structures for document intelligence and user interaction tracking
 */

// Core Intelligence Data Types
export interface DocumentIntelligenceSession {
  session_id: string
  timestamp: string
  document: DocumentMetadata
  intelligence_session: IntelligenceAnalysis
  user_interactions: UserInteraction[]
  context_signals: ContextSignals
  learning_data: LearningData
}

export interface DocumentMetadata {
  path: string
  name: string
  type: string
  size: number
  hash: string
  vault: string
  context_id?: string
  last_modified: string
}

export interface IntelligenceAnalysis {
  session_type: 'smart_annotation' | 'entity_extraction' | 'content_analysis'
  ai_model: string
  processing_time_ms: number
  confidence_score: number
  extracted_entities: ExtractedEntity[]
  key_insights: KeyInsight[]
  content_summary?: string
  related_documents?: string[]
}

export interface ExtractedEntity {
  entity: string
  type: EntityType
  confidence: number
  user_selected: boolean
  context: string
  category?: string
  relationships?: EntityRelationship[]
}

export type EntityType = 
  | 'content_category'
  | 'technical_concept' 
  | 'action_item'
  | 'person'
  | 'organization'
  | 'location'
  | 'date'
  | 'technology'
  | 'methodology'
  | 'requirement'
  | 'feature'
  | 'issue'
  | 'solution'
  | 'other'

export interface EntityRelationship {
  target_entity: string
  relationship_type: 'related_to' | 'part_of' | 'implements' | 'requires' | 'conflicts_with'
  strength: number
}

export interface KeyInsight {
  insight: string
  importance: 'low' | 'medium' | 'high' | 'critical'
  category: 'summary' | 'analysis' | 'recommendation' | 'warning' | 'opportunity'
  confidence: number
  supporting_entities?: string[]
}

export interface UserInteraction {
  action: UserActionType
  timestamp: string
  data?: any
  session_id: string
}

export type UserActionType =
  | 'tag_selection'
  | 'tag_deselection'
  | 'annotation_edit'
  | 'annotation_save'
  | 'smart_annotation_trigger'
  | 'ai_chat_initiate'
  | 'extract_trigger'
  | 'document_open'
  | 'document_close'
  | 'session_export'

export interface ContextSignals {
  user_intent: string
  document_importance: 'low' | 'medium' | 'high' | 'critical'
  workflow_stage: string
  related_documents: string[]
  access_frequency: number
  time_spent_seconds: number
  interaction_depth: 'surface' | 'moderate' | 'deep'
}

export interface LearningData {
  entity_patterns: Record<string, EntityPattern>
  user_preferences: UserPreferences
  workflow_patterns: WorkflowPattern[]
  content_clusters: ContentCluster[]
}

export interface EntityPattern {
  frequency: number
  co_occurrences: string[]
  contexts: string[]
  user_selection_rate: number
  confidence_trend: number[]
}

export interface UserPreferences {
  preferred_tags: string[]
  annotation_style: 'concise' | 'detailed' | 'technical'
  detail_level: 'low' | 'medium' | 'high'
  ai_confidence_threshold: number
  auto_tag_enabled: boolean
}

export interface WorkflowPattern {
  pattern_id: string
  sequence: string[]
  frequency: number
  success_rate: number
  context: string
}

export interface ContentCluster {
  cluster_id: string
  theme: string
  documents: string[]
  entities: string[]
  strength: number
  last_updated: string
}

// Storage Schema Types
export interface IntelligenceIndex {
  version: string
  vault_path: string
  total_documents: number
  total_sessions: number
  last_updated: string
  entity_index: Record<string, EntityIndexEntry>
  document_profiles: Record<string, DocumentProfile>
}

export interface EntityIndexEntry {
  entity: string
  type: EntityType
  total_occurrences: number
  documents: string[]
  user_selection_rate: number
  average_confidence: number
  first_seen: string
  last_seen: string
}

export interface DocumentProfile {
  document_hash: string
  path: string
  total_sessions: number
  key_entities: string[]
  user_engagement_score: number
  content_importance: 'low' | 'medium' | 'high' | 'critical'
  workflow_connections: WorkflowConnection[]
  last_analyzed: string
}

export interface WorkflowConnection {
  target_document: string
  relationship: 'implements' | 'references' | 'extends' | 'conflicts' | 'supports'
  strength: number
  evidence: string[]
}

// Master.md Integration Types
export interface MasterDocumentIntelligence {
  document_intelligence: {
    total_sessions: number
    key_entities: string[]
    user_engagement_score: number
    content_importance: 'low' | 'medium' | 'high' | 'critical'
    workflow_connections: WorkflowConnection[]
    intelligence_summary: string
    last_updated: string
  }
}

// UI Component Types
export interface IntelligenceUIState {
  isAnalyzing: boolean
  currentSession: DocumentIntelligenceSession | null
  suggestedTags: ExtractedEntity[]
  selectedTags: ExtractedEntity[]
  confidenceScore: number
  insights: KeyInsight[]
  sessionHistory: DocumentIntelligenceSession[]
  currentSessionIndex: number
}

export interface SmartAnnotationConfig {
  ai_model: string
  confidence_threshold: number
  max_entities: number
  enable_relationships: boolean
  auto_save: boolean
  include_content_analysis: boolean
}

// File Details Overlay Specific Types
export interface FileIntelligenceData {
  document_hash: string
  file_path: string
  file_name: string
  file_type: string
  vault_name: string
  context_id?: string
  last_updated: string
  entity_selections: EntitySelection[]
  smart_annotations: SmartAnnotation[]
  user_notes: UserNote[]
  ai_analysis: AIAnalysisResult | null
  interaction_history: FileInteraction[]
  annotation_navigation: AnnotationNavigation
}

export interface AnnotationNavigation {
  total_notes: number
  current_note_index: number
  note_order: string[] // Array of annotation_ids in display order
  last_viewed_note: string | null
}

export interface EntitySelection {
  entity_id: string
  entity_text: string
  entity_type: EntityType
  confidence: number
  is_selected: boolean
  selection_timestamp: string
  color_category: 'primary' | 'secondary' | 'tertiary' | 'default'
  rank: number
  context_snippet: string
}

export interface SmartAnnotation {
  annotation_id: string
  note_number: number // For "Note #3" display
  title: string // Auto-generated or user-defined title
  content: string
  is_ai_generated: boolean
  generation_source: 'full_document' | 'selected_text' | 'user_prompt'
  generation_prompt?: string // If user provided custom prompt
  timestamp: string
  last_edited: string
  selected_text?: string
  position_info?: TextPosition
  tags: string[]
  confidence?: number
  ai_model_used?: string
  processing_time_ms?: number
}

export interface UserNote {
  note_id: string
  content: string
  timestamp: string
  last_edited: string
  selected_text?: string
  position_info?: TextPosition
  tags: string[]
}

export interface AIAnalysisResult {
  session_id: string
  model_used: string
  processing_time_ms: number
  confidence_score: number
  summary: string
  key_insights: string[]
  suggested_entities: ExtractedEntity[]
  content_analysis: ContentAnalysis
  timestamp: string
}

export interface ContentAnalysis {
  document_type: string
  primary_topics: string[]
  complexity_score: number
  readability_score: number
  key_concepts: string[]
  action_items: string[]
  relationships: EntityRelationship[]
}

export interface TextPosition {
  start_offset: number
  end_offset: number
  line_number?: number
  column_number?: number
  page_number?: number
}

export interface FileInteraction {
  interaction_id: string
  action_type: 'entity_select' | 'entity_deselect' | 'annotation_create' | 'annotation_edit' | 'ai_analyze' | 'text_select'
  timestamp: string
  data: any
  session_id: string
}

// Service Interface Types
export interface IntelligenceCollectionService {
  analyzeDocument(filePath: string, content: string, config?: SmartAnnotationConfig): Promise<IntelligenceAnalysis>
  saveSession(session: DocumentIntelligenceSession): Promise<boolean>
  loadSessionHistory(documentHash: string): Promise<DocumentIntelligenceSession[]>
  updateEntityIndex(entities: ExtractedEntity[], documentHash: string): Promise<void>
  generateInsights(content: string, entities: ExtractedEntity[]): Promise<KeyInsight[]>
  exportIntelligenceData(documentHash: string): Promise<any>

  // File Details Overlay specific methods
  saveFileIntelligence(data: FileIntelligenceData): Promise<boolean>
  loadFileIntelligence(documentHash: string): Promise<FileIntelligenceData | null>
  updateEntitySelections(documentHash: string, selections: EntitySelection[]): Promise<boolean>
  addSmartAnnotation(documentHash: string, annotation: SmartAnnotation): Promise<boolean>
  updateSmartAnnotation(documentHash: string, annotationId: string, content: string): Promise<boolean>
}

// Error Types
export interface IntelligenceError {
  code: 'ANALYSIS_FAILED' | 'STORAGE_ERROR' | 'MODEL_UNAVAILABLE' | 'INVALID_DOCUMENT'
  message: string
  details?: any
}
