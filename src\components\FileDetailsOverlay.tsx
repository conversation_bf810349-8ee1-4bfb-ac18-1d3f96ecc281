import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { DocumentViewer } from './DocumentViewer'
import { TextPosition } from '../types/intelligenceTypes'

interface FileDetailsOverlayProps {
  filePath?: string
  onClose: () => void
}

interface FileTypeInfo {
  type: 'pdf' | 'markdown' | 'text' | 'image' | 'code' | 'unsupported'
  extension: string
  mimeType?: string
  canExtractText: boolean
  canAnnotate: boolean
  requiresProcessing: boolean
}

export const FileDetailsOverlay: React.FC<FileDetailsOverlayProps> = ({
  filePath,
  onClose
}) => {
  const [selectedEntities, setSelectedEntities] = useState<Set<string>>(new Set())
  const [annotationText, setAnnotationText] = useState('')
  const [isEditingAnnotation, setIsEditingAnnotation] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(100)
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null)
  const [extractedContent, setExtractedContent] = useState<string>('')

  if (!filePath) return null

  // Extract file name from path
  const fileName = filePath.split('/').pop() || filePath

  // Comprehensive file type detection
  const detectFileType = (fileName: string): FileTypeInfo => {
    const extension = fileName.split('.').pop()?.toLowerCase() || ''

    // PDF files
    if (extension === 'pdf') {
      return {
        type: 'pdf',
        extension,
        mimeType: 'application/pdf',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true
      }
    }

    // Markdown files
    if (['md', 'markdown'].includes(extension)) {
      return {
        type: 'markdown',
        extension,
        mimeType: 'text/markdown',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false
      }
    }

    // Text files
    if (['txt', 'log', 'csv', 'yaml', 'yml', 'ini', 'cfg', 'conf'].includes(extension)) {
      return {
        type: 'text',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false
      }
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'].includes(extension)) {
      return {
        type: 'image',
        extension,
        mimeType: `image/${extension === 'jpg' ? 'jpeg' : extension}`,
        canExtractText: true, // OCR possible
        canAnnotate: false,
        requiresProcessing: true
      }
    }

    // Code files
    if (['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'json', 'xml', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(extension)) {
      return {
        type: 'code',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false
      }
    }

    // Unsupported
    return {
      type: 'unsupported',
      extension,
      canExtractText: false,
      canAnnotate: false,
      requiresProcessing: false
    }
  }

  // Initialize file type detection
  React.useEffect(() => {
    const typeInfo = detectFileType(fileName)
    setFileTypeInfo(typeInfo)
  }, [fileName])

  // Mock entities data - ranked by probability with top 3 pre-selected
  const mockEntities = [
    { id: 'ui-design', text: 'UI Design', confidence: 0.95, selected: true, color: 'primary' },
    { id: 'specifications', text: 'Specifications', confidence: 0.88, selected: true, color: 'secondary' },
    { id: 'requirements', text: 'Requirements', confidence: 0.82, selected: true, color: 'tertiary' },
    { id: 'component-library', text: 'Component Library', confidence: 0.75, selected: false, color: 'default' },
    { id: 'design-tokens', text: 'Design Tokens', confidence: 0.68, selected: false, color: 'default' },
    { id: 'architecture', text: 'Architecture', confidence: 0.61, selected: false, color: 'default' },
    { id: 'testing', text: 'Testing', confidence: 0.54, selected: false, color: 'default' },
    { id: 'documentation', text: 'Documentation', confidence: 0.47, selected: false, color: 'default' }
  ]

  const handleEntityToggle = (entityId: string) => {
    const newSelected = new Set(selectedEntities)
    if (newSelected.has(entityId)) {
      newSelected.delete(entityId)
    } else {
      newSelected.add(entityId)
    }
    setSelectedEntities(newSelected)
  }

  const handleSmartAnnotation = () => {
    setAnnotationText('AI-generated insights about this document will appear here. This document appears to contain comprehensive UI design specifications with detailed component requirements and implementation guidelines.')
  }

  const handleAskAI = () => {
    // Navigate to Chat page with document context
    console.log('Navigating to Chat with document:', fileName)
    onClose()
  }

  const handleExtract = () => {
    console.log('Extracting text from:', fileName)
  }

  const closePDFViewer = () => {
    onClose()
  }

  // Initialize with pre-selected entities based on probability ranking
  useEffect(() => {
    const initialSelected = new Set<string>()
    mockEntities.forEach(entity => {
      if (entity.selected) {
        initialSelected.add(entity.id)
      }
    })
    setSelectedEntities(initialSelected)
  }, [])

  return (
    <div className="absolute inset-0 bg-gray-900 z-50 flex">
      {/* Center Column - PDF Viewer */}
      <div className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
        {/* PDF Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button onClick={closePDFViewer} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-lg">{fileName}</span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setZoomLevel(Math.max(50, zoomLevel - 10))}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-400 text-sm" />
            </button>
            <span className="text-gray-400 text-sm">{zoomLevel}%</span>
            <button
              onClick={() => setZoomLevel(Math.min(200, zoomLevel + 10))}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-400 text-sm" />
            </button>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>

        {/* Enhanced Document Viewer */}
        <DocumentViewer
          filePath={filePath}
          fileName={fileName}
          onTextSelection={(text, position) => {
            setAnnotationText(text)
            console.log('Text selected:', text, position)
          }}
          onContentLoad={(content) => {
            console.log('Document content loaded:', content.length, 'characters')
          }}
          onContentExtracted={(extractedContent, metadata) => {
            setExtractedContent(extractedContent)
            console.log('Content extracted for AI:', {
              length: extractedContent.length,
              metadata
            })
          }}
        />
      </div>

      {/* Right Column - File Details */}
      <div className="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]" style={{resize: 'horizontal'}}>

        {/* File Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">{fileName}</span>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-500 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Private Document
              </div>
            </button>
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                System Operations
              </div>
            </button>
            <button onClick={closePDFViewer} className="p-1 hover:bg-gray-700 rounded transition-colors">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>


        {/* Tags Section */}
        <div className="p-3 border-b border-tertiary/50">
          <p className="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
          <div className="flex flex-wrap gap-1 mb-2">
            {mockEntities.slice(0, 3).map((entity) => (
              <span
                key={entity.id}
                onClick={() => handleEntityToggle(entity.id)}
                className={`px-2 py-0.5 text-xs rounded-full border font-medium cursor-pointer transition-colors ${
                  selectedEntities.has(entity.id)
                    ? entity.color === 'primary'
                      ? 'bg-primary/20 text-primary border-primary/30'
                      : entity.color === 'secondary'
                      ? 'bg-secondary/20 text-secondary border-secondary/30'
                      : entity.color === 'tertiary'
                      ? 'bg-tertiary/20 text-tertiary border-tertiary/30'
                      : 'bg-gray-600/20 text-gray-300 border-gray-600/30'
                    : 'bg-gray-700/20 text-gray-400 border-gray-600/30 hover:bg-gray-600/20'
                }`}
              >
                {entity.text}
              </span>
            ))}
          </div>
          <div className="flex flex-wrap gap-1">
            {mockEntities.slice(3).map((entity) => (
              <span
                key={entity.id}
                onClick={() => handleEntityToggle(entity.id)}
                className={`px-2 py-0.5 text-xs rounded-full border font-medium cursor-pointer transition-colors ${
                  selectedEntities.has(entity.id)
                    ? 'bg-gray-600/20 text-gray-300 border-gray-600/30'
                    : 'bg-gray-700/20 text-gray-400 border-gray-600/30 hover:bg-gray-600/20'
                }`}
              >
                {entity.text}
              </span>
            ))}
          </div>
        </div>


        {/* Smart Annotation Section */}
        <div className="flex-1 flex flex-col p-3 border-b border-tertiary/50">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-supplement1">Smart Annotation</h3>
            <button
              onClick={handleSmartAnnotation}
              className="px-3 py-1 bg-primary hover:bg-primary/80 text-gray-900 text-xs font-medium rounded transition-colors flex items-center gap-1"
            >
              <FontAwesomeIcon icon={ICONS.brain} className="text-xs" />
              Analyze
            </button>
          </div>

          <div className="w-full h-full rounded-lg border border-tertiary/50 bg-white flex flex-col overflow-hidden">
            <div className="flex-1 p-3 overflow-y-auto">
              {isEditingAnnotation ? (
                <textarea
                  value={annotationText}
                  onChange={(e) => setAnnotationText(e.target.value)}
                  className="w-full h-full resize-none border-none outline-none text-sm text-gray-700 bg-transparent"
                  placeholder="Add your insights about this document..."
                />
              ) : (
                <div className="text-sm text-gray-700">
                  {annotationText || (
                    <div className="text-center text-gray-400 py-8">
                      <FontAwesomeIcon icon={ICONS.brain} className="text-2xl mb-2" />
                      <p>Click "Analyze" to generate AI insights</p>
                      <p className="text-xs mt-1">or click the edit icon to add your own notes</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="border-t border-gray-200 p-2 flex justify-between items-center">
              <button
                onClick={() => setIsEditingAnnotation(!isEditingAnnotation)}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.edit} className="text-gray-500 text-sm" />
              </button>
              {isEditingAnnotation && (
                <div className="flex gap-1">
                  <button
                    onClick={() => setIsEditingAnnotation(false)}
                    className="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 rounded transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      setIsEditingAnnotation(false)
                      console.log('Saving annotation:', annotationText)
                    }}
                    className="px-2 py-1 text-xs bg-primary hover:bg-primary/80 text-gray-900 rounded transition-colors"
                  >
                    Save
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>


        {/* Action Buttons */}
        <div className="p-4 space-y-3">
          <button
            onClick={handleAskAI}
            className="w-full bg-secondary hover:bg-secondary/80 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <FontAwesomeIcon icon={ICONS.comments} />
            Ask AI
          </button>

          <div className="flex gap-2">
            <button
              onClick={handleExtract}
              className="flex-1 bg-gray-700 hover:bg-gray-600 text-supplement1 font-medium py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              <FontAwesomeIcon icon={ICONS.fileText} className="text-sm" />
              Extract
            </button>

            <div className="flex-1 text-xs text-gray-400 px-2 py-2 flex items-center">
              Vault: Your First Context Vault
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


